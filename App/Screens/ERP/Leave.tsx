import React, { useEffect, useState } from 'react';
import {
  View,
  SafeAreaView,
  TextInput,
  TouchableOpacity,
  Text,
  FlatList,
  Modal,
  StyleSheet,
  Alert,
} from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import moment from 'moment';
import IndexStyle from '../../Theme/IndexStyle';
import { PrimaryColors } from '../../Utils/Constants';
import CommonDateTimePicker from '../../CommonComponents/CommonDateTimePicker';
import CurvHeader from '../../CommonComponents/CurvHeader';
import {
  getLeaves,
  applyLeave,
  updateLeave,
  deleteLeave,
} from '../../services/studentClassesService';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Toast from 'react-native-simple-toast';
import { getStatusStyle } from '../../Utils/Helper';

const Leave = () => {
  const { styles: themeStyles, isDarkMode } = IndexStyle();
  const navigation = useNavigation();

  const [leaveDate, setLeaveDate] = useState<Date | null>(null);
  const [leaveReason, setLeaveReason] = useState('');
  const [leaves, setLeaves] = useState<any[]>([]);
  const [editId, setEditId] = useState<number | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const formatDate = (date: Date | null) => {
    if (!date) return '';
    return moment(date).format('YYYY-MM-DD');
  };

  const fetchLeaves = async (newPage: number = 1, isRefresh = false) => {
    if (!isRefresh && (loading || !hasMore)) return;
    setLoading(true);
    try {
      const res = await getLeaves(newPage);
      if (newPage === 1) {
        setLeaves(res.data);
      } else {
        setLeaves(prev => [...prev, ...res.data]);
      }

      setPage(res.current_page);
      setHasMore(res.current_page < res.last_page);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLeaves();
  }, []);

  const handleSubmit = async () => {
    if (!leaveDate || !leaveReason.trim()) return;
    const formattedDate = formatDate(leaveDate);

    try {
      let response: any;
      if (editId) {
        await updateLeave(editId, formattedDate, leaveReason);
        setLeaves(prev =>
          prev.map(leave =>
            leave.id === editId
              ? { ...leave, leave_date: formattedDate, reason: leaveReason }
              : leave,
          ),
        );
        Toast.show('Leave updated successfully', Toast.SHORT);
      } else {
        response = await applyLeave(formattedDate, leaveReason);
        console.log(response);
        setLeaves(prev => [
          { ...response.data, leave_date: formattedDate, reason: leaveReason },
          ...prev,
        ]);
        Toast.show('Leave applied successfully', Toast.SHORT);
      }

      setLeaveDate(null);
      setLeaveReason('');
      setEditId(null);
      setModalVisible(false);
    } catch (err: any) {
      Toast.show(
        err.response?.data?.message || 'Something went wrong',
        Toast.SHORT,
      );
    }
  };

  const handleEdit = (leave: any) => {
    setLeaveDate(new Date(leave.leave_date));
    setLeaveReason(leave.reason);
    setEditId(leave.id);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await deleteLeave(id);
      setLeaves(prev => prev.filter(leave => leave.id !== id));
      Toast.show('Leave deleted successfully', Toast.SHORT);
    } catch (err: any) {
      Toast.show(
        err.response?.data?.message || 'Something went wrong',
        Toast.SHORT,
      );
    }
  };

  const loadMore = () => {
    if (hasMore && !loading) {
      fetchLeaves(page + 1);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await fetchLeaves(1, true); // force fetch without hasMore/loaded check
    } catch (error) {
      console.error('Refresh error:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const renderLeaveItem = ({ item }: { item: any }) => (
    <View style={styles.leaveItem}>
      <Text style={styles.leaveText}>Date: {item.leave_date}</Text>
      <Text style={styles.leaveText}>Reason: {item.reason}</Text>

      {/* Status Text */}
      <Text style={[styles.leaveText, getStatusStyle(item.leave_status)]}>
        Status: {item.leave_status}
      </Text>

      {/* Action buttons only if not approved */}
      {item.leave_status !== 'APPROVED' && (
        <View style={styles.actionRow}>
          <TouchableOpacity onPress={() => handleEdit(item)}>
            <Text style={styles.editText}>Edit</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              Alert.alert(
                'Delete Confirmation',
                'Are you sure you want to delete this leave?',
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Delete',
                    style: 'destructive',
                    onPress: () => handleDelete(item.id),
                  },
                ],
              );
            }}>
            <Text style={styles.deleteText}>Delete</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: isDarkMode ? '#161616' : PrimaryColors.WHITE,
        }}>
        <CurvHeader
          title="Leave"
          isBack={true}
          onBackPress={() => navigation.goBack()}
        />

        <FlatList
          contentContainerStyle={{ padding: 16 }}
          data={leaves}
          keyExtractor={item => item.id.toString()}
          renderItem={renderLeaveItem}
          refreshing={refreshing}
          onRefresh={handleRefresh}
          ListHeaderComponent={
            <Text style={styles.listHeader}>Leave List</Text>
          }
          onEndReached={loadMore}
          onEndReachedThreshold={0.2}
          ListFooterComponent={
            loading ? (
              <Text style={{ textAlign: 'center', marginTop: 10 }}>
                Loading...
              </Text>
            ) : null
          }
        />

        <TouchableOpacity
          style={styles.floatingButton}
          onPress={() => setModalVisible(true)}>
          <Ionicons name="add" size={32} color="#fff" />
        </TouchableOpacity>

        <Modal visible={modalVisible} transparent animationType="slide">
          <View style={styles.modalContainer}>
            <View style={styles.formContainer}>
              <Text style={styles.headerText}>
                {editId ? 'Edit Leave' : 'Apply Leave'}
              </Text>
              <CommonDateTimePicker
                label="Leave Date"
                value={leaveDate}
                innerText={formatDate(leaveDate) || 'Select Date'}
                mode="date"
                onChange={setLeaveDate}
                minimumDate={new Date()}
              />
              <TextInput
                style={styles.textInput}
                placeholder="Enter reason"
                placeholderTextColor="#888"
                value={leaveReason}
                onChangeText={setLeaveReason}
                multiline
              />
              <TouchableOpacity
                style={styles.submitButton}
                onPress={handleSubmit}
                disabled={!leaveDate || !leaveReason.trim()}>
                <Text style={styles.submitButtonText}>
                  {editId ? 'Update' : 'Submit'}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  setModalVisible(false);
                  setEditId(null);
                  setLeaveDate(null);
                  setLeaveReason('');
                }}>
                <Text
                  style={{ textAlign: 'center', marginTop: 12, color: '#888' }}>
                  Cancel
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default Leave;

const styles = StyleSheet.create({
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    width: '90%',
  },
  headerText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 12,
    marginTop: 12,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    marginTop: 16,
    backgroundColor: '#FF914D',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  listHeader: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  leaveItem: {
    padding: 12,
    backgroundColor: '#F9F9F9',
    borderRadius: 8,
    marginBottom: 10,
  },
  leaveText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 20,
  },
  editText: {
    color: '#1976D2',
    fontWeight: '600',
  },
  deleteText: {
    color: '#E53935',
    fontWeight: '600',
  },
  floatingButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    backgroundColor: '#FF914D',
    borderRadius: 28,
    width: 56,
    height: 56,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
