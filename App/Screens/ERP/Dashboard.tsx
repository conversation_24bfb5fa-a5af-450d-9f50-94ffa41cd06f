import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import {SafeAreaProvider, SafeAreaView} from 'react-native-safe-area-context';
import {PrimaryColors, IMAGE_CONSTANT} from '../../Utils/Constants';
import IndexStyle from '../../Theme/IndexStyle';
import {getProfile} from '../../services/studentClassesService';
const {width, height} = Dimensions.get('window');

const categories = [
  {title: 'Attendance', image: IMAGE_CONSTANT.ATTENDENCE, route: 'Attendance'},
  {title: 'Classwork', image: IMAGE_CONSTANT.CLASSWORK, route: 'Classwork'},
  {title: 'Homework', image: IMAGE_CONSTANT.HOMEWORK, route: 'Homework'},
  {title: 'Leaves', image: IMAGE_CONSTANT.LEAVES, route: 'Leave'},
  {title: 'Timetable', image: IMAGE_CONSTANT.TIMETABLE, route: 'Timetable'}
];

const Dashboard = ({navigation}) => {
  const {isDarkMode} = IndexStyle();
  const [profileDetails, setProfileDetails] = useState({});

  const getStudentDetails = async () => {
    try {
      const response = await getProfile('**********');
      console.log(response);
      setProfileDetails(response.data); // adjust if `response` is already `data`
    } catch (error) {
      console.error('Error fetching student details:', error);
    }
  };

  useEffect(() => {
    getStudentDetails();
  }, []);

  const renderItem = ({item}) => (
    <TouchableOpacity
      style={[
        Basestyles.card,
        {backgroundColor: isDarkMode ? '#313131' : PrimaryColors.WHITE},
      ]}
      onPress={() => {
        navigation.navigate(item.route);
      }}>
      <Image source={item.image} style={Basestyles.cardImage} />

      <Text
        style={[
          Basestyles.cardTitle,
          {color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK},
        ]}>
        {item.title}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaProvider style={{backgroundColor: PrimaryColors.BLACK}}>
      <SafeAreaView
        style={[Basestyles.container, {backgroundColor: PrimaryColors.BLACK}]}
        edges={['left', 'right']}>
        <View style={Basestyles.header}>
          <View style={{}}>
            <Text style={Basestyles.dashboardTitle}>Dashboard</Text>
            <View style={Basestyles.userRow}>
              <View style={Basestyles.userInfo}>
                <Text style={[Basestyles.welcomeText]}>Welcome,</Text>
                <Text style={[Basestyles.nameText, {paddingTop: 4}]}>
                  {profileDetails?.student?.firstName}{' '}
                  {profileDetails?.student?.lastName}
                </Text>
                <View style={{paddingTop: 10}}>
                  <Text style={Basestyles.detailText}>
                    ClassName : {profileDetails?.academicInfo?.get_class.className}
                  </Text>
                </View>
                <View style={{paddingTop: 10}}>
                  <Text style={Basestyles.detailText}>
                    Class : {profileDetails?.academicInfo?.get_classroom.class_name}
                  </Text>
                  <Text style={Basestyles.detailText}>
                    Contact no : {profileDetails?.student?.contact}
                  </Text>
                </View>
              </View>
              <View>
                <Image
                  source={{uri: 'https://i.pravatar.cc/200'}}
                  style={Basestyles.avatar}
                />
              </View>
            </View>
          </View>
        </View>
        <View
          style={[
            Basestyles.gridContainer,
            {backgroundColor: isDarkMode ? '#161616' : PrimaryColors.WHITE},
          ]}>
          <View style={{marginBottom: 20}}>
            <Text
              style={{
                fontSize: 18,
                color: isDarkMode ? PrimaryColors.WHITE : '#505050',
                fontWeight: 'medium',
                letterSpacing: 0.2,
              }}>
              Quick Links
            </Text>
          </View>
          <FlatList
            data={categories}
            renderItem={renderItem}
            keyExtractor={(item, index) => index.toString()}
            numColumns={3}
            columnWrapperStyle={{justifyContent: 'space-around'}}
            contentContainerStyle={{paddingBottom: 40}}
          />
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const Basestyles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    height: height * 0.32,
    paddingTop: '12%',
    padding: 16,
  },
  dashboardTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 10,
    textAlign: 'center',
  },
  userRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: '3%',
  },
  userInfo: {
    flex: 1,
    marginRight: 10,
  },
  welcomeText: {
    color: '#FF914D',
    fontSize: 20,
  },
  nameText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    paddingTop: 2,
  },
  detailText: {
    color: '#969696',
    fontSize: 14,
    marginBottom: 5,
    letterSpacing: 0.2,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  gridContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 20,
    height: height * 0.68,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    justifyContent: 'space-around',
  },
  card: {
    height: height * 0.12,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
    width: (width - 64) / 3,
    elevation: 4,
    shadowColor: PrimaryColors.BLACK,
    shadowOpacity: 0.5,
    shadowOffset: {width: 0, height: 0},
    shadowRadius: 4,
  },
  cardImage: {
    width: '60%',
    height: '75%',
    resizeMode: 'contain',
  },
  cardTitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: '2%',
  },
});

export default Dashboard;
