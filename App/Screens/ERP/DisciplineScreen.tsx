import React from 'react';
import {View, SafeAreaView, ScrollView, StyleSheet, Text} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import CurvHeader from '../../CommonComponents/CurvHeader';
import {PrimaryColors} from '@Utils/Constants';
import IndexStyle from '../../Theme/IndexStyle';
import moment from 'moment';

const dateFormat = (date: Date) => {
  return moment(date).format('DD/MM/YYYY');
};

interface Discipline {
  date: Date;
  id: number;
  discipline_issue: string;
}

export interface Params {
  key: string;
  name: string;
  params: {
    title: string;
    info?: any;
  };
  path: string;
}

const DisciplineScreen = () => {
  const {params}: Params = useRoute();
  console.log(params.info);
  const navigation = useNavigation();
  const {isDarkMode} = IndexStyle();

  const data = params?.info ?? [];

  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={[
          styles.safeArea,
          {backgroundColor: isDarkMode ? '#161616' : PrimaryColors.WHITE},
        ]}>
        <CurvHeader
          title="Discipline Issue"
          isBack
          onBackPress={() => navigation.goBack()}
        />
        <ScrollView contentContainerStyle={styles.content}>
          {data.length === 0 ? (
            <Text style={styles.noDataText}>No Data Found</Text>
          ) : (
            data.map((item: Discipline) => (
              <View style={styles.cardContainer} key={item.id}>
                <View style={styles.card}>
                  <View style={styles.cardRow}>
                    <Text style={styles.label}>Date:</Text>
                    <Text style={styles.value}>{dateFormat(item.date)}</Text>
                  </View>
                  <View style={styles.cardRow}>
                    <Text style={styles.label}>Issue:</Text>
                    <Text style={styles.value}>{item.discipline_issue}</Text>
                  </View>
                </View>
              </View>
            ))
          )}
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export {DisciplineScreen};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  noDataText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 40,
    color: '#999',
  },
  cardContainer: {
    marginBottom: 16,
  },
  card: {
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 0.5,
    borderBottomColor: '#ccc',
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  value: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000',
    flex: 1,
    textAlign: 'right',
    paddingLeft: 10,
  },
});
