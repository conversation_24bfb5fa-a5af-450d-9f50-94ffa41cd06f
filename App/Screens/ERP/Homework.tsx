import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import CalendarStrip from 'react-native-calendar-strip';
import moment from 'moment';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {getHomework} from '../../services/studentClassesService';
import CurvHeader from '../../CommonComponents/CurvHeader';
import Badge from '../../CommonComponents/Badge';
import RenderHTML from 'react-native-render-html';
import {Homework} from '../../Utils/Interfaces';
import {useNavigation} from '@react-navigation/native';

const {width} = Dimensions.get('window');

const HomeWorkScreen = () => {
  const [selectedDate, setSelectedDate] = useState(
    moment().format('YYYY-MM-DD'),
  );
  const [works, setWorks] = useState<Homework[]>([]);
  const navigation = useNavigation();

  useEffect(() => {
    loadHomework(selectedDate);
  }, [selectedDate]);

  const loadHomework = async (date: string) => {
    const {data} = await getHomework(date);
    setWorks(data);
  };

  const renderWorkItem = (item: Homework) => (
    <View style={styles.card} key={item.id}>
      <View style={styles.headerRow}>
        <Badge
          text={item.subject_name}
          backgroundColor="#1976D2"
          textColor="#fff"
        />
        <Text style={styles.teacherText}>{item.faculty_name}</Text>
      </View>
      <View style={styles.section}>
        <Text style={styles.label}>Homework: {item.title}</Text>
        <RenderHTML
          contentWidth={width - 40}
          source={{html: item.description || '<p>No Content</p>'}}
        />
      </View>
    </View>
  );

  return (
    <SafeAreaProvider>
      <SafeAreaView style={styles.safeArea}>
        <CurvHeader
          title="Homework"
          isBack
          onBackPress={() => navigation.goBack()}
        />
        <View style={styles.calendarContainer}>
          <CalendarStrip
            scrollable
            style={styles.calendarStrip}
            calendarHeaderStyle={{display: 'none'}}
            dateNumberStyle={styles.dateNumber}
            dateNameStyle={styles.dateName}
            highlightDateNumberStyle={styles.highlightDateNumber}
            highlightDateNameStyle={styles.highlightDateName}
            selectedDate={moment(selectedDate)}
            onDateSelected={date => setSelectedDate(date.format('YYYY-MM-DD'))}
            minDate={moment('2020-01-01')}
            maxDate={moment('2030-12-31')}
            dayContainerStyle={styles.dayContainer}
          />
        </View>

        <ScrollView contentContainerStyle={styles.content}>
          {works.length === 0 ? (
            <Text style={styles.noData}>No data found</Text>
          ) : (
            works.map(renderWorkItem)
          )}
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export {HomeWorkScreen};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  calendarContainer: {
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderColor: '#E0E0E0',
  },
  calendarStrip: {
    height: 100,
    paddingHorizontal: 10,
  },
  dateNumber: {
    fontSize: 16,
    color: '#000',
  },
  dateName: {
    fontSize: 12,
    color: '#000',
  },
  highlightDateNumber: {
    color: '#FFFFFF',
    backgroundColor: '#1976D2',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  highlightDateName: {
    color: '#1976D2',
  },
  dayContainer: {
    paddingVertical: 6,
  },
  content: {
    padding: 16,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: {width: 0, height: 2},
    elevation: 2,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  teacherText: {
    fontSize: 14,
    color: '#555',
  },
  section: {
    marginTop: 8,
  },
  label: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 4,
  },
  noData: {
    textAlign: 'center',
    color: '#888',
    marginTop: 20,
  },
});
