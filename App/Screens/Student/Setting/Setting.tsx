import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  Easing,
  Linking,
} from 'react-native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useDispatch } from 'react-redux';
import Ionicons from 'react-native-vector-icons/Ionicons';

import { PrimaryColors } from '../../../Utils/Constants';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import { setDarkMode } from '../../../Redux/themeSlice';
import NavigationHeader from '../../../CommonComponents/NavigationHeader';

const Setting = () => {
  const navigation = useNavigation<any>();
  const dispatch = useDispatch();
  const [isDarkMode, setIsDarkMode] = useState(false);
  const animation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const getStoredTheme = async () => {
      try {
        const storedMode = await AsyncStorage.getItem('isDarkMode');
        const parsedMode = storedMode !== null ? JSON.parse(storedMode) : false;
        setIsDarkMode(parsedMode);
        dispatch(setDarkMode(parsedMode));
        animation.setValue(parsedMode ? 1 : 0);
      } catch (error) {
        console.error('Failed to load theme mode:', error);
      }
    };
    getStoredTheme();
  }, []);

  const toggleTheme = async () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    dispatch(setDarkMode(newMode));
    await AsyncStorage.setItem('isDarkMode', JSON.stringify(newMode));
    Animated.timing(animation, {
      toValue: newMode ? 1 : 0,
      duration: 200,
      easing: Easing.out(Easing.circle),
      useNativeDriver: false,
    }).start();
  };

  const translateX = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 25],
  });
  const getTrackStyle = (isDarkMode: boolean) => ({
    width: 50,
    height: 30,
    borderRadius: 15,
    padding: 2,
    justifyContent: 'center',
    backgroundColor: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
  });

  const getKnobStyle = (translateX: Animated.AnimatedInterpolation) => ({
    width: 20,
    height: 20,
    borderRadius: 15,
    backgroundColor: isDarkMode ? PrimaryColors.BLACK : PrimaryColors.WHITE,
    transform: [{ translateX }],
  });

  const logout = () => {
    Alert.alert(
      strings.Setting.LOGOUT,
      strings.Setting.AREYOUSUREYOUWANTTOLOGOUT,
      [
        { text: strings.Setting.CANCEL, style: 'cancel' },
        {
          text: strings.Setting.LOGOUT,
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.removeItem('token');
              navigation.navigate('Login');
            } catch (error) {
              console.error('Logout error:', error);
            }
          },
        },
      ],
    );
  };

  return (
    <SafeAreaProvider>
      <NavigationHeader title="Setting" onBackPress={() => { }} />
      <SafeAreaView
        style={[
          styles.container,
          {
            backgroundColor: isDarkMode
              ? PrimaryColors.BLACK
              : PrimaryColors.WHITE,
          },
        ]}
        edges={['left', 'right']}>
        <View style={styles.settingRow}>
          <View style={styles.iconLabel}>
            <Ionicons
              name={isDarkMode ? 'sunny-outline' : 'moon-outline'}
              size={30}
              color={PrimaryColors.ORANGE}
            />
            <Text
              style={[
                styles.labelText,
                { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK },
              ]}>
              Dark Mode
            </Text>
          </View>

          <TouchableOpacity onPress={toggleTheme}>
            <View style={getTrackStyle(isDarkMode)}>
              <Animated.View style={getKnobStyle(translateX)} />
            </View>
          </TouchableOpacity>
        </View>

        <View style={styles.divider} />

        <TouchableOpacity
          onPress={() => Linking.openURL('https://www.uest.in/privacy-policy')}>
          <View style={styles.iconLabel}>
            <Ionicons
              name="shield-checkmark-outline"
              size={30}
              color={PrimaryColors.ORANGE}
            />
            <Text
              style={[
                styles.labelText,
                {
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                },
              ]}>
              Privacy Policy
            </Text>
          </View>
        </TouchableOpacity>
        <View style={styles.divider} />

        <View />

        <TouchableOpacity
          onPress={() =>
            Linking.openURL('https://www.uest.in/terms-and-conditions')
          }>
          <View style={styles.iconLabel}>
            <Ionicons
              name="document-text-outline"
              size={30}
              color={PrimaryColors.ORANGE}
            />
            <Text
              style={[
                styles.labelText,
                {
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                },
              ]}>
              Terms & Conditions
            </Text>
          </View>
        </TouchableOpacity>
        <View style={styles.divider} />

        <TouchableOpacity onPress={logout}>
          <View style={styles.iconLabel}>
            <Ionicons
              name="log-out-outline"
              size={30}
              color={PrimaryColors.ORANGE}
            />
            <Text
              style={[
                styles.labelText,
                { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK },
              ]}>
              {strings.Setting.LOGOUT}
            </Text>
          </View>
        </TouchableOpacity>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default Setting;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    paddingVertical: 5,
  },
  divider: {
    height: 1,
    backgroundColor: '#808080',
    marginTop: '3%',
    marginBottom: '4%',
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  iconLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  labelText: {
    fontSize: 22,
  },
});
