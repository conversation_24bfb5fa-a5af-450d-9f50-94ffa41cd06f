/* eslint-disable react-native/no-inline-styles */
import React, {useState} from 'react';
import {
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import api from '../../../config/api';
import Toast from 'react-native-simple-toast';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import RazorpayCheckout from 'react-native-razorpay';
import ID from '../../../../secret_payment_key';
import {PrimaryColors} from '../../../Utils/Constants';
import {useNavigation} from '@react-navigation/native';
import CommonMessageCard from '../../../CommonComponents/CommonMessageCard';
import NavigationHeader from '../../../CommonComponents/NavigationHeader';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import IndexStyle from '../../../Theme/IndexStyle';

const AddCoinsScreen = () => {
  const [amount, setAmount] = useState('');
  const [amountError, setAmountError] = useState('');

  const navigation = useNavigation<any>();
    const {isDarkMode} = IndexStyle();


  const id = ID.ID;

  const handlePayment = async () => {
    try {
      setAmountError('');
      const trimmedAmount = amount.trim();
      const numericAmount = Number(trimmedAmount);

      if (!trimmedAmount) {
        setAmountError('Please enter an amount.');
        return;
      }

      if (isNaN(numericAmount)) {
        setAmountError('Please enter a valid number.');
        return;
      }

      if (numericAmount <= 0) {
        setAmountError('Amount must be greater than 0.');
        return;
      }
      const paiseAmount = Number(amount) * 100;

      const response = await api.StudentCoin.createOrder(paiseAmount);
      const data = await response.json();

      console.log('Backend order response:', JSON.stringify(data, null, 2));

      if (!response.ok || !data || !data.order || !data.order.id) {
        console.error('Order creation failed', data);
        Toast.show(strings.PaymentList.FAILDINITIATEPAYMENT, Toast.SHORT);
        return;
      }
      console.log(data.order.id);
      const options = {
        key: id.RAZORPAY_KEY_ID,
        amount: data.order.amount || paiseAmount,
        currency: 'INR',
        name: 'UEST Coins',
        description: 'Add Uest Coins',
        order_id: data.order.id,
        theme: {color: '#3399cc'},
      };

      RazorpayCheckout.open(options)
        .then(async (paymentData: any) => {
          console.log('Payment successful', paymentData);
          console.log(
            'Available keys in paymentData:',
            Object.keys(paymentData),
          );

          const verifyRes = await api.StudentCoin.verifyPayment({
            razorpay_order_id: data.order.id,
            razorpay_payment_id: paymentData.razorpay_payment_id,
            razorpay_signature: paymentData.razorpay_signature,
            amount: paiseAmount,
          });
          console.log(verifyRes);

          const verifyData = await verifyRes.json();
          if (verifyRes.ok) {
            Toast.show(strings.PaymentList.PAYMENTVERIFIED, Toast.SHORT);
            setAmount('');
            navigation.navigate('Payment');
          } else {
            console.error('Verification failed', verifyData);
            Toast.show(strings.PaymentList.VARIFICATIONFAILD, Toast.SHORT);
          }
        })
        .catch(error => {
          console.error('Razorpay Error', error);
          Toast.show(strings.PaymentList.PAYMENTFAILED, Toast.SHORT);
        });
    } catch (err) {
      console.error('Payment flow error:', err);
      Toast.show(strings.PaymentList.SOMETHINGWENTWRONG, Toast.SHORT);
    }
  };

  return (
    <SafeAreaProvider>
      {/* <NavigationHeader title="Add Coins"  onBackPress={() => {
    // navigation.navigate('Payment');
    navigation.goBack();
  }} /> */}
  <NavigationHeader title="Add Coins" isBack={true} onBackPress={()=>{
          navigation.goBack();
        }}/>
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: isDarkMode
            ? PrimaryColors.BLACK
            : PrimaryColors.WHITE,
          padding: '5%',
        }}>
        <Text
          style={{
            textAlign: 'center',
            fontSize: 14,
            marginTop: '30%',
            color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
          }}>
          Enter Amount
        </Text>

        <TextInput
        keyboardType="numeric"
          style={{
            textAlign: 'center',
            fontSize: 38,
            fontWeight: '700',
            borderBottomWidth: 1,
            borderBottomColor: PrimaryColors.GRAYSHADOW,
            marginHorizontal: 40,
            marginTop: 4,
            marginBottom: 60,
            color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
          }}
          placeholder="₹0.00"
          placeholderTextColor={
            isDarkMode ? PrimaryColors.GRAYSHADOW : PrimaryColors.BLACK
          }
          keyboardType="numeric"
          value={amount}
          onChangeText={setAmount}
        />

        {amountError !== '' && (
          <CommonMessageCard
            icon="warning"
            text={amountError}
            backgroundColor={PrimaryColors.WARNINGNBACKGROUNDYELLOW}
            textColor={PrimaryColors.WARNINGTEXTBROWN}
          />
        )}

        <TouchableOpacity
          onPress={handlePayment}
          style={{
            flexDirection: 'row',
            backgroundColor: '#FF914D',
            paddingVertical: 14,
            borderRadius: 10,
            justifyContent: 'center',
            alignItems: 'center',
            alignSelf: 'center',
            width: '80%',
            position: 'absolute',
            bottom: 20,
            marginBottom: '3%',
          }}>
          <Icon
            name="security"
            size={20}
            color={isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK}
          />
          <Text
            style={{
              color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              fontSize: 16,
              fontWeight: '600',
              marginLeft: 8,
            }}>
            Secure Payment
          </Text>
        </TouchableOpacity>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default AddCoinsScreen;
