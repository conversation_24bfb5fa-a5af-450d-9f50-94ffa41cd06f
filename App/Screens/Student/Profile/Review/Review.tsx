/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import {SafeAreaView, SafeAreaProvider} from 'react-native-safe-area-context';
import strings from '../../../../Utils/LocalizedStrings/LocalizedStrings';
import {PrimaryColors} from '../../../../Utils/Constants';
import IndexStyle from '../../../../Theme/IndexStyle';
import Button from '../../../../CommonComponents/Button';
import api from '../../../../config/api';
import CommonTextInput from '../../../../CommonComponents/CommonTextInput';
import NavigationHeader from '../../../../CommonComponents/NavigationHeader';
import {useNavigation} from '@react-navigation/native';
import {imgBaseUrl} from '../../../../config/apiUrl';
import {useRoute} from '@react-navigation/native';
import Toast from 'react-native-simple-toast';
import {ScrollView} from 'react-native-gesture-handler';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getProfileData } from '../../../../services/classService';
import { getReviewData, submitReviewData } from '../../../../services/reviewService';
import StarRating from '../../../../CommonComponents/StarRating';
import ProfileHeader from '../../../../CommonComponents/ProfileHeader';
import ReviewCard from '../../../../CommonComponents/ReviewCard';

const {width: screenWidth} = Dimensions.get('window');
const isTablet = screenWidth >= 768;
const isSmallScreen = screenWidth < 375;

const Review = () => {
  const navigation = useNavigation<any>();
  const [rating, setRating] = useState(0);
  const [message, setMessage] = useState('');
  const [error, setError] = useState({rating: false, message: false});
  const [review, setReview] = useState<any[]>([]);
  const {isDarkMode} = IndexStyle();
  const [profileData, setProfileData] = useState<any>(null);
  const [storedUserData, setStoredUserData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const route = useRoute<any>();
  const {classId, avgRating, totalReview} = route.params;

  console.log('ClassID', classId, avgRating, totalReview);

  useEffect(() => {
    getProfileDataHandler();
    fetchReviewtData();
    fetchStoredUserData();
  }, []);

  const getProfileDataHandler = async () => {
    try {
      const data = await getProfileData(classId);
      setProfileData(data);
      console.log('class data', data);
    } catch (err) {
      console.log('Profile fetch error:', err);
    }
  };

  const fetchReviewtData = async (pageNumber = 1) => {
    try {
      const data = await getReviewData(classId, pageNumber, 10);
      setReview(data.reviews || []);
      console.log('PARSED  DATA::', data);
      console.log('Review  DATA::', data.reviews);
    } catch (err) {
      console.log('ERROR IN GET REVIEW DATA::', err);
    }
  };

  const fetchStoredUserData = async () => {
    try {
      const userDataString = await AsyncStorage.getItem('userData');
      console.log('Raw AsyncStorage userData:', userDataString);
      if (userDataString) {
        const userData = JSON.parse(userDataString);
        console.log('Parsed userData:', userData);
        setStoredUserData(userData.data);
      } else {
        console.log('No userData found in AsyncStorage');
      }
    } catch (err) {
      console.error('Error retrieving userData from AsyncStorage:', err);
    }
  };

  const handleSubmit = async () => {
    const messageError = !message.trim();
    const ratingError = rating === 0;

    setError({
      message: messageError,
      rating: ratingError,
    });

    if (messageError || ratingError) {
      return;
    }

    const studentId = storedUserData?.user?.id;
    const studentName = `${storedUserData?.user?.firstName} ${storedUserData?.user?.lastName}`;

    const alreadyReviewed = review.some(item => item.studentId === studentId);

    if (alreadyReviewed) {
      Toast.show('You have already submitted a review', Toast.LONG);
      return;
    }

    const addData = {
      rating,
      message,
      classId,
      studentId,
      studentName,
    };

    console.log('Adding REVIEW with:', addData);

    try {
      setIsLoading(true);
      await submitReviewData(addData);
      Toast.show('Review submitted successfully!', Toast.SHORT);
      setMessage('');
      setRating(0);
      fetchReviewtData();
    } catch (err) {
      console.log('Review submit error:', err);
      Toast.show('Something went wrong', Toast.LONG);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = (id: string) => {
    Alert.alert(
      'Remove from Review',
      'Are you sure you want to delete this class from your Review?',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await api.deleteReviewData(id);
              if (response.ok) {
                Toast.show('Review Deleted Successfully', Toast.SHORT);
                setReview([]);
                fetchReviewtData();
              } else {
                console.log('Failed to delete data. Status:', response.status);
              }
            } catch (err) {
              console.log('ERROR IN DELETE DATA::', err);
            }
          },
        },
      ],
      {cancelable: true},
    );
  };



  return (
    <SafeAreaProvider
      style={{
        backgroundColor: isDarkMode ? PrimaryColors.BLACK : PrimaryColors.WHITE,
      }}>
      <NavigationHeader
        title={strings.Review.REVIEW}
        isBack={true}
        onBackPress={() => navigation.goBack()}
      />

      <SafeAreaView style={{flex: 1}}>
        <ProfileHeader
          profileData={profileData}
          avgRating={avgRating}
          totalReviews={totalReview}
          imageBaseUrl={imgBaseUrl}
        />
        <View style={localStyles.contentContainer}>
        <ScrollView
          style={localStyles.scrollView}
          contentContainerStyle={localStyles.scrollContent}
          showsVerticalScrollIndicator={false}>
          <View
            style={[
              localStyles.reviewFormCard,
              {
                borderColor: isDarkMode ? '#3E3E3E' : '#E0E0E0',
                backgroundColor: isDarkMode
                  ? '#1B1B1B'
                  : PrimaryColors.WHITE,
              },
            ]}>
            <Text style={[
              localStyles.formTitle,
              {color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK},
            ]}>
              Rate Your Experience
            </Text>

            <StarRating
              rating={rating}
              interactive={true}
              onRatingPress={setRating}
              size={28}
              color="#FD904B"
              style={localStyles.starRating}
            />

            {error.rating && (
              <Text style={localStyles.errorText}>
                {strings.Review.MINIMUMRATING}
              </Text>
            )}

            <CommonTextInput
              label={strings.Review.YOURMESSAGE}
              placeholder={strings.Review.SHAREYOUREXPERIENCE}
              value={message}
              onChangeText={text => {
                setMessage(text);
                if (text.trim()) {
                  setError(prev => ({...prev, message: false}));
                }
              }}
              multiline
              style={localStyles.textInput}
            />

            {error.message && (
              <Text style={localStyles.errorText}>
                {strings.Review.MESSAGEREQUIRED}
              </Text>
            )}

            <View style={localStyles.submitContainer}>
              {isLoading ? (
                <ActivityIndicator size="large" color="#FD904B" />
              ) : (
                <Button
                  title={strings.Review.SUBMITREVIEW}
                  onPress={handleSubmit}
                />
              )}
            </View>
          </View>

          {/* Reviews List */}
          <View style={localStyles.reviewsSection}>
            <Text style={[
              localStyles.sectionTitle,
              {color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK},
            ]}>
              Reviewssss ({review.length})
            </Text>

            {review.map(item => (
              <ReviewCard
                key={item.id}
                review={item}
                currentUserId={storedUserData?.user?.id}
                onDelete={handleDelete}
              />
            ))}
          </View>
        </ScrollView>
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default Review;

const localStyles = StyleSheet.create({
  contentContainer: {
    flex: 1,
    borderTopLeftRadius: 26,
    borderTopRightRadius: 26,
    marginTop: 8,
    overflow: 'hidden',
    backgroundColor: 'transparent',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  reviewFormCard: {
    backgroundColor: '#fff',
    marginHorizontal: isTablet ? 32 : isSmallScreen ? 12 : 16,
    marginTop: 20,
    borderRadius: 20,
    padding: isTablet ? 32 : isSmallScreen ? 16 : 24,
    borderWidth: 0,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    maxWidth: isTablet ? 600 : undefined,
    alignSelf: isTablet ? 'center' : 'stretch',
  },
  formTitle: {
    fontSize: isTablet ? 26 : isSmallScreen ? 20 : 22,
    fontWeight: '800',
    textAlign: 'center',
    marginBottom: isTablet ? 24 : 20,
    letterSpacing: 0.3,
  },
  starRating: {
    justifyContent: 'center',
    marginVertical: isTablet ? 24 : 20,
  },
  textInput: {
    width: '100%',
    marginTop: isTablet ? 24 : 20,
  },
  errorText: {
    color: '#FF6B6B',
    fontSize: isSmallScreen ? 12 : 13,
    marginTop: 12,
    textAlign: 'center',
    fontWeight: '500',
  },
  submitContainer: {
    alignItems: 'center',
    marginTop: isTablet ? 28 : 24,
  },
  reviewsSection: {
    marginTop: isTablet ? 40 : 32,
    paddingHorizontal: isTablet ? 32 : 0,
  },
  sectionTitle: {
    fontSize: isTablet ? 24 : isSmallScreen ? 18 : 20,
    fontWeight: '800',
    marginBottom: isTablet ? 24 : 20,
    marginHorizontal: isTablet ? 0 : 16,
    letterSpacing: 0.3,
    textAlign: isTablet ? 'center' : 'left',
  },
  // Legacy styles (keeping for compatibility)
  testimonialCard: {
    backgroundColor: '#fff',
    width: '100%',
    borderRadius: 10,
    padding: 15,
    marginTop: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  label: {
    marginTop: 10,
    fontWeight: '600',
  },
  card: {
    borderRadius: 12,
    marginTop: 20,
    alignItems: 'flex-start',
    paddingTop: 10,
  },
  avatar: {
    width: 110,
    height: 110,
    borderRadius: 55,
    overflow: 'hidden',
    marginBottom: 10,
    borderWidth: 1,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
  },
});
