import axiosClass from '../config/axiosClass';

export async function getProfile() {
  const response = await axiosClass.get('/get-student-details');

  return response.data;
}

export async function getAttendance(startDate: string, endDate: string) {
  const response = await axiosClass.get('/get-attendance', {
    params: {startDate, endDate},
  });

  return response.data;
}

export async function getDisciplineIssue(startDate: string, endDate: string) {
  const response = await axiosClass.get('/get-discipline-issue', {
    params: {startDate, endDate},
  });

  return response.data;
}

export async function getClasswork(date: string) {
  const response = await axiosClass.get('/get-classwork', {
    params: {date},
  });

  return response.data;
}

export async function getHomework(date: string) {
  const response = await axiosClass.get('/get-homework', {
    params: {date},
  });

  return response.data;
}

export async function getTimetable(date: string) {
  const response = await axiosClass.get('/get-timetable', {
    params: {date},
  });

  return response.data;
}

export const getLeaves = async (page: number = 1) => {
  const response = await axiosClass.get(`/get-leavelist?page=${page}`);
  return response.data.data;
};

export const applyLeave = async (leaveDate: string, reason: string) => {
  const response = await axiosClass.post('/create-leave', {
    leave_date: leaveDate,
    reason: reason,
  });
  return response.data;
};

export const updateLeave = async (
  id: number,
  leaveDate: string,
  reason: string,
) => {
  const response = await axiosClass.put(`/update-leave/${id}`, {
    leave_date: leaveDate,
    reason: reason,
  });
  return response.data;
};

export const deleteLeave = async (id: number) => {
  const response = await axiosClass.delete(`/delete-leave/${id}`);
  return response.data;
};

export const getLeaveById = async (id: number) => {
  const response = await axiosClass.get(`/get-leave-by-id/${id}`);
  return response.data;
};
