import moment from "moment";

export const getDateCalenderFormat = (date : string) => {
  const m = moment(date);
  return {
    dateString: m.format('YYYY-MM-DD'),
    day: m.date(), 
    month: m.month() + 1,
    timestamp: m.valueOf(),
    year: m.year(),
  };
};

export const getStatusStyle = (status: string) => {
  switch (status) {
    case 'APPROVED':
      return { color: 'green', fontWeight: 'bold' };
    case 'REJECTED':
      return { color: 'red', fontWeight: 'bold' };
    case 'PENDING':
      return { color: 'orange', fontWeight: 'bold' };
    default:
      return { color: 'black' };
  }
};