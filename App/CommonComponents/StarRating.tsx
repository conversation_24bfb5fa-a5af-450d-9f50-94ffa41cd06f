import React from 'react';
import {View, TouchableOpacity, StyleSheet} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {PrimaryColors} from '../Utils/Constants';

interface StarRatingProps {
  rating: number;
  maxStars?: number;
  size?: number;
  color?: string;
  interactive?: boolean;
  onRatingPress?: (rating: number) => void;
  style?: any;
}

const StarRating: React.FC<StarRatingProps> = ({
  rating,
  maxStars = 5,
  size = 24,
  color = '#FD904B',
  interactive = false,
  onRatingPress,
  style,
}) => {
  const handleStarPress = (starIndex: number) => {
    if (interactive && onRatingPress) {
      onRatingPress(starIndex);
    }
  };

  const renderStar = (starIndex: number) => {
    const isFilled = starIndex <= rating;
    const StarComponent = interactive ? TouchableOpacity : View;

    return (
      <StarComponent
        key={starIndex}
        onPress={() => handleStarPress(starIndex)}
        style={styles.starContainer}
        disabled={!interactive}>
        <Ionicons
          name={isFilled ? 'star' : 'star-outline'}
          size={size}
          color={color}
        />
      </StarComponent>
    );
  };

  return (
    <View style={[styles.container, style]}>
      {Array.from({length: maxStars}, (_, index) => renderStar(index + 1))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starContainer: {
    marginHorizontal: 2,
  },
});

export default StarRating;
