import React from 'react';
import {View, Text, TouchableOpacity, Image, StyleSheet, Dimensions} from 'react-native';
import {PrimaryColors, IMAGE_CONSTANT} from '../Utils/Constants';
import IndexStyle from '../Theme/IndexStyle';
import StarRating from './StarRating';

const {width: screenWidth} = Dimensions.get('window');
const isTablet = screenWidth >= 768;
const isSmallScreen = screenWidth < 375;

interface ReviewCardProps {
  review: {
    id: string;
    studentName: string;
    studentId: string;
    rating: number;
    message: string;
    createdAt: string;
  };
  currentUserId?: string;
  onDelete?: (id: string) => void;
  style?: any;
}

const ReviewCard: React.FC<ReviewCardProps> = ({
  review,
  currentUserId,
  onDelete,
  style,
}) => {
  const {isDarkMode} = IndexStyle();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const canDelete = currentUserId && review.studentId === currentUserId;

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: isDarkMode ? '#1B1B1B' : PrimaryColors.WHITE,
          borderColor: isDarkMode ? '#3E3E3E' : '#E0E0E0',
          shadowColor: isDarkMode ? '#000' : '#000',
        },
        style,
      ]}>
      {/* Header with name and delete button */}
      <View style={styles.header}>
        <View style={styles.userInfo}>
          <Text
            style={[
              styles.studentName,
              {
                color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              },
            ]}>
            {review.studentName || 'Anonymous'}
          </Text>
          <StarRating rating={review.rating || 0} size={16} color="#FD904B" />
        </View>

        {canDelete && onDelete && (
          <TouchableOpacity
            onPress={() => onDelete(review.id)}
            style={styles.deleteButton}>
            <Image
              resizeMode="contain"
              source={IMAGE_CONSTANT.DELETE}
              style={styles.deleteIcon}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Review message */}
      <Text
        style={[
          styles.message,
          {
            color: isDarkMode ? '#E0E0E0' : '#333333',
          },
        ]}>
        {review.message || 'No message provided'}
      </Text>

      {/* Date */}
      <Text
        style={[
          styles.date,
          {
            color: isDarkMode ? '#B1B1B1' : '#666666',
          },
        ]}>
        Posted on: {formatDate(review.createdAt)}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: isTablet ? 24 : isSmallScreen ? 16 : 20,
    marginVertical: isTablet ? 12 : 10,
    marginHorizontal: isTablet ? 32 : isSmallScreen ? 12 : 16,
    borderWidth: 0,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
    maxWidth: isTablet ? 600 : undefined,
    alignSelf: isTablet ? 'center' : 'stretch',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: isTablet ? 20 : 16,
  },
  userInfo: {
    flex: 1,
  },
  studentName: {
    fontSize: isTablet ? 19 : isSmallScreen ? 15 : 17,
    fontWeight: '800',
    marginBottom: isTablet ? 10 : 8,
    letterSpacing: 0.3,
  },
  deleteButton: {
    padding: isTablet ? 8 : 6,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  deleteIcon: {
    width: isTablet ? 20 : 18,
    height: isTablet ? 20 : 18,
    tintColor: '#FF6B6B',
  },
  message: {
    fontSize: isTablet ? 17 : isSmallScreen ? 14 : 15,
    lineHeight: isTablet ? 26 : isSmallScreen ? 20 : 22,
    marginBottom: isTablet ? 20 : 16,
    fontWeight: '500',
    letterSpacing: 0.2,
  },
  date: {
    fontSize: isTablet ? 15 : isSmallScreen ? 12 : 13,
    fontWeight: '600',
    opacity: 0.7,
  },
});

export default ReviewCard;
