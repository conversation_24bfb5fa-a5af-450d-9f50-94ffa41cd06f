import React from 'react';
import {View, Text, Image, StyleSheet, Dimensions} from 'react-native';
import {PrimaryColors} from '../Utils/Constants';
import IndexStyle from '../Theme/IndexStyle';
import DefaultClassesLogo from './DefaultClassesLogo';
import StarRating from './StarRating';

const {width: screenWidth} = Dimensions.get('window');
const isTablet = screenWidth >= 768;
const isSmallScreen = screenWidth < 375;

interface ProfileHeaderProps {
  profileData: any;
  avgRating: number;
  totalReviews?: number;
  imageBaseUrl?: string;
  style?: any;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  profileData,
  avgRating,
  totalReviews,
  imageBaseUrl,
  style,
}) => {
  const {isDarkMode} = IndexStyle();

  return (
    <View style={[styles.container, style]}>
      <View style={styles.card}>
        {/* Profile Avatar */}
        <View
          style={[
            styles.avatarContainer,
            {
              borderColor: isDarkMode ? '#3E3E3E' : '#E0E0E0',
              shadowColor: isDarkMode ? '#000' : '#000',
            },
          ]}>
          {profileData?.ClassAbout?.profilePhoto ? (
            <Image
              source={{
                uri: `${imageBaseUrl}/${profileData.ClassAbout.profilePhoto}`,
              }}
              style={styles.avatarImage}
              resizeMode="cover"
            />
          ) : (
            <DefaultClassesLogo
              firstName={profileData?.firstName}
              lastName={profileData?.lastName}
            />
          )}
        </View>

        {/* Profile Info */}
        <View style={styles.infoContainer}>
          <Text
            style={[
              styles.nameText,
              {
                color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              },
            ]}>
            {profileData?.firstName} {profileData?.lastName}
          </Text>

          <View style={styles.ratingContainer}>
            <StarRating rating={avgRating} size={20} color="#FD904B" />
            <Text
              style={[
                styles.ratingText,
                {
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                },
              ]}>
              {avgRating.toFixed(1)}/5
            </Text>
          </View>

          {totalReviews !== undefined && (
            <Text
              style={[
                styles.reviewCountText,
                {
                  color: isDarkMode ? '#B1B1B1' : '#666666',
                },
              ]}>
              {totalReviews} review{totalReviews !== 1 ? 's' : ''}
            </Text>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: isTablet ? 32 : isSmallScreen ? 16 : 20,
    paddingVertical: isTablet ? 20 : 16,
  },
  card: {
    alignItems: 'center',
    paddingVertical: isTablet ? 32 : 24,
    paddingHorizontal: isTablet ? 32 : 20,
    borderRadius: 16,
    backgroundColor: 'transparent',
    maxWidth: isTablet ? 500 : undefined,
    alignSelf: isTablet ? 'center' : 'stretch',
  },
  avatarContainer: {
    width: isTablet ? 140 : isSmallScreen ? 100 : 120,
    height: isTablet ? 140 : isSmallScreen ? 100 : 120,
    borderRadius: isTablet ? 70 : isSmallScreen ? 50 : 60,
    overflow: 'hidden',
    marginBottom: isTablet ? 24 : 20,
    borderWidth: 4,
    borderColor: '#FD904B',
    shadowColor: '#FD904B',
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 8,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
  },
  infoContainer: {
    alignItems: 'center',
  },
  nameText: {
    fontSize: isTablet ? 30 : isSmallScreen ? 22 : 26,
    fontWeight: '800',
    marginBottom: isTablet ? 16 : 12,
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: isTablet ? 12 : 8,
  },
  ratingText: {
    fontSize: isTablet ? 24 : isSmallScreen ? 18 : 20,
    fontWeight: '700',
    marginLeft: isTablet ? 12 : 10,
    letterSpacing: 0.3,
  },
  reviewCountText: {
    fontSize: isTablet ? 17 : isSmallScreen ? 13 : 15,
    fontWeight: '600',
    opacity: 0.8,
  },
});

export default ProfileHeader;
